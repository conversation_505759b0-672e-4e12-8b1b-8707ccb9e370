import React, { useState } from 'react';

// Modal component for displaying full content
const ContentModal = ({ isOpen, onClose, title, content }) => {
  if (!isOpen) return null;

  return (
    <div
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 10000
      }}
      onClick={onClose}
    >
      <div
        style={{
          backgroundColor: 'white',
          borderRadius: '8px',
          padding: '20px',
          maxWidth: '90%',
          maxHeight: '90vh',
          overflow: 'auto',
          position: 'relative'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '20px',
          borderBottom: '1px solid #eee',
          paddingBottom: '10px'
        }}>
          <h3 style={{ margin: 0 }}>{title}</h3>
          <button
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              fontSize: '24px',
              cursor: 'pointer',
              color: '#666'
            }}
          >
            ×
          </button>
        </div>
        <div style={{
          fontFamily: 'monospace',
          fontSize: '14px',
          lineHeight: '1.5',
          whiteSpace: 'pre-wrap',
          wordBreak: 'break-word',
          maxHeight: '768px',
          overflowY: 'auto'
        }}>
          {content}
        </div>
      </div>
    </div>
  );
};

const EtrayResultsTable = ({ evaluations, onAnnotationUpdate }) => {
  const [expandedRows, setExpandedRows] = useState(new Set());
  const [editingAnnotation, setEditingAnnotation] = useState(null);
  const [annotationText, setAnnotationText] = useState('');
  const [modalState, setModalState] = useState({
    isOpen: false,
    title: '',
    content: ''
  });

  const toggleRow = (id) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(id)) {
      newExpanded.delete(id);
    } else {
      newExpanded.add(id);
    }
    setExpandedRows(newExpanded);
  };

  const startEditingAnnotation = (evaluation) => {
    setEditingAnnotation(evaluation.id);
    setAnnotationText(evaluation.annotation || '');
  };

  const saveAnnotation = async (evaluationId) => {
    try {
      await onAnnotationUpdate(evaluationId, annotationText);
      setEditingAnnotation(null);
      setAnnotationText('');
    } catch (error) {
      alert('Failed to update annotation');
    }
  };

  const cancelEditingAnnotation = () => {
    setEditingAnnotation(null);
    setAnnotationText('');
  };

  const openModal = (title, content) => {
    setModalState({
      isOpen: true,
      title,
      content
    });
  };

  const closeModal = () => {
    setModalState({
      isOpen: false,
      title: '',
      content: ''
    });
  };

  const formatTimestamp = (timestamp) => {
    if (!timestamp) return 'N/A';
    return new Date(timestamp).toLocaleString();
  };

  const getStatusBadge = (status) => {
    const styles = {
      completed: { backgroundColor: '#d4edda', color: '#155724', border: '1px solid #c3e6cb' },
      in_progress: { backgroundColor: '#fff3cd', color: '#856404', border: '1px solid #ffeaa7' },
      error: { backgroundColor: '#f8d7da', color: '#721c24', border: '1px solid #f5c6cb' }
    };

    const style = styles[status] || styles.in_progress;

    return (
      <span style={{
        ...style,
        padding: '4px 8px',
        borderRadius: '4px',
        fontSize: '12px',
        fontWeight: 'bold'
      }}>
        {status === 'in_progress' ? '⏳ Processing' : 
         status === 'completed' ? '✅ Complete' : 
         status === 'error' ? '❌ Error' : status}
      </span>
    );
  };

  const renderOutput = (evaluation) => {
    if (evaluation.status !== 'completed' || !evaluation.output) {
      return <span style={{ color: '#6c757d', fontStyle: 'italic' }}>No output available</span>;
    }

    try {
      const output = typeof evaluation.output === 'string' ? JSON.parse(evaluation.output) : evaluation.output;
      
      if (output.result && Array.isArray(output.result)) {
        return (
          <div>
            <h4 style={{ margin: '10px 0', color: '#495057' }}>Analysis Results:</h4>
            {output.result.map((participant, index) => (
              <div key={index} style={{
                border: '1px solid #dee2e6',
                borderRadius: '4px',
                padding: '10px',
                marginBottom: '10px',
                backgroundColor: '#f8f9fa'
              }}>
                <h5 style={{ margin: '0 0 8px 0', color: '#495057' }}>
                  {participant.participant_name || `Participant ${participant.user_id || index + 1}`}
                  {participant.overall_score && (
                    <span style={{ marginLeft: '10px', color: '#28a745', fontWeight: 'bold' }}>
                      Overall Score: {participant.overall_score}
                    </span>
                  )}
                </h5>
                
                {participant.detail_scores && participant.detail_scores.length > 0 && (
                  <div style={{ marginTop: '8px' }}>
                    <strong>Competency Scores:</strong>
                    <ul style={{ margin: '5px 0', paddingLeft: '20px' }}>
                      {participant.detail_scores.map((competency, compIndex) => (
                        <li key={compIndex} style={{ marginBottom: '4px' }}>
                          <strong>{competency.competency_name}:</strong> {competency.average_score}
                          {competency.aspects && competency.aspects.length > 0 && (
                            <ul style={{ marginTop: '4px', paddingLeft: '15px' }}>
                              {competency.aspects.map((aspect, aspectIndex) => (
                                <li key={aspectIndex} style={{ fontSize: '12px', color: '#6c757d' }}>
                                  {aspect.aspect_name}: Level {aspect.scale_level}
                                </li>
                              ))}
                            </ul>
                          )}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            ))}
          </div>
        );
      } else {
        return (
          <div style={{ fontFamily: 'monospace', fontSize: '12px', color: '#495057' }}>
            {JSON.stringify(output, null, 2)}
          </div>
        );
      }
    } catch (error) {
      return (
        <div style={{ fontFamily: 'monospace', fontSize: '12px', color: '#495057' }}>
          {evaluation.output}
        </div>
      );
    }
  };

  if (!evaluations || evaluations.length === 0) {
    return (
      <div style={{
        border: '1px solid #ddd',
        borderRadius: '8px',
        padding: '40px',
        textAlign: 'center',
        backgroundColor: '#f8f9fa',
        color: '#6c757d'
      }}>
        <h3 style={{ margin: '0 0 10px 0' }}>📧 No E-tray Evaluations Yet</h3>
        <p style={{ margin: 0 }}>
          Run your first E-tray analysis using the evaluation runner above.
        </p>
      </div>
    );
  }

  return (
    <div style={{
      border: '1px solid #ddd',
      borderRadius: '8px',
      backgroundColor: 'white',
      overflow: 'hidden'
    }}>
      <div style={{
        backgroundColor: '#f8f9fa',
        padding: '15px 20px',
        borderBottom: '1px solid #ddd'
      }}>
        <h3 style={{ margin: 0, color: '#495057' }}>
          📧 E-tray Analysis Results ({evaluations.length})
        </h3>
      </div>

      <div style={{ maxHeight: '600px', overflowY: 'auto' }}>
        {evaluations.map((evaluation) => (
          <div key={evaluation.id} style={{
            borderBottom: '1px solid #eee'
          }}>
            {/* Main row */}
            <div
              style={{
                padding: '15px 20px',
                cursor: 'pointer',
                backgroundColor: expandedRows.has(evaluation.id) ? '#f8f9fa' : 'white',
                borderLeft: expandedRows.has(evaluation.id) ? '4px solid #007bff' : '4px solid transparent'
              }}
              onClick={() => toggleRow(evaluation.id)}
            >
              <div style={{
                display: 'grid',
                gridTemplateColumns: '1fr auto auto auto',
                gap: '15px',
                alignItems: 'center'
              }}>
                <div>
                  <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>
                    E-tray Analysis #{evaluation.id}
                  </div>
                  <div style={{ fontSize: '12px', color: '#6c757d' }}>
                    {formatTimestamp(evaluation.timestamp)}
                  </div>
                  {evaluation.formattedInput && (
                    <div style={{ fontSize: '12px', color: '#6c757d', marginTop: '4px' }}>
                      {evaluation.formattedInput.length > 100 
                        ? `${evaluation.formattedInput.substring(0, 100)}...`
                        : evaluation.formattedInput}
                    </div>
                  )}
                </div>

                <div>
                  {getStatusBadge(evaluation.status)}
                </div>

                <div style={{ fontSize: '12px', color: '#6c757d' }}>
                  {evaluation.prompt1Version && evaluation.prompt2Version && (
                    <div>
                      Prompts: v{evaluation.prompt1Version}, v{evaluation.prompt2Version}
                    </div>
                  )}
                </div>

                <div style={{ fontSize: '18px', color: '#6c757d' }}>
                  {expandedRows.has(evaluation.id) ? '▼' : '▶'}
                </div>
              </div>
            </div>

            {/* Expanded content */}
            {expandedRows.has(evaluation.id) && (
              <div style={{
                padding: '0 20px 20px 20px',
                backgroundColor: '#f8f9fa',
                borderTop: '1px solid #eee'
              }}>
                {/* Input section */}
                <div style={{ marginBottom: '20px' }}>
                  <h4 style={{ margin: '0 0 10px 0', color: '#495057' }}>Input Data:</h4>
                  {evaluation.formattedInput && (
                    <div style={{ marginBottom: '10px' }}>
                      <button
                        onClick={() => openModal('Input Data', evaluation.formattedInput)}
                        style={{
                          padding: '6px 12px',
                          border: '1px solid #007bff',
                          borderRadius: '4px',
                          backgroundColor: 'white',
                          color: '#007bff',
                          cursor: 'pointer',
                          fontSize: '12px'
                        }}
                      >
                        📄 View Full Input
                      </button>
                    </div>
                  )}
                </div>

                {/* Output section */}
                <div style={{ marginBottom: '20px' }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
                    <h4 style={{ margin: 0, color: '#495057' }}>Analysis Output:</h4>
                    {evaluation.output && evaluation.status === 'completed' && (
                      <button
                        onClick={() => openModal('Full Analysis Output', 
                          typeof evaluation.output === 'string' ? evaluation.output : JSON.stringify(evaluation.output, null, 2))}
                        style={{
                          padding: '6px 12px',
                          border: '1px solid #28a745',
                          borderRadius: '4px',
                          backgroundColor: 'white',
                          color: '#28a745',
                          cursor: 'pointer',
                          fontSize: '12px'
                        }}
                      >
                        📊 View Full Output
                      </button>
                    )}
                  </div>
                  <div style={{
                    border: '1px solid #dee2e6',
                    borderRadius: '4px',
                    padding: '10px',
                    backgroundColor: 'white',
                    maxHeight: '300px',
                    overflowY: 'auto'
                  }}>
                    {renderOutput(evaluation)}
                  </div>
                </div>

                {/* Annotation section */}
                <div>
                  <h4 style={{ margin: '0 0 10px 0', color: '#495057' }}>Annotation:</h4>
                  {editingAnnotation === evaluation.id ? (
                    <div>
                      <textarea
                        value={annotationText}
                        onChange={(e) => setAnnotationText(e.target.value)}
                        placeholder="Add your annotation here..."
                        style={{
                          width: '100%',
                          minHeight: '80px',
                          padding: '8px',
                          border: '1px solid #ced4da',
                          borderRadius: '4px',
                          fontSize: '14px',
                          resize: 'vertical'
                        }}
                      />
                      <div style={{ marginTop: '8px', display: 'flex', gap: '8px' }}>
                        <button
                          onClick={() => saveAnnotation(evaluation.id)}
                          style={{
                            padding: '6px 12px',
                            border: 'none',
                            borderRadius: '4px',
                            backgroundColor: '#28a745',
                            color: 'white',
                            cursor: 'pointer',
                            fontSize: '12px'
                          }}
                        >
                          Save
                        </button>
                        <button
                          onClick={cancelEditingAnnotation}
                          style={{
                            padding: '6px 12px',
                            border: '1px solid #6c757d',
                            borderRadius: '4px',
                            backgroundColor: 'white',
                            color: '#6c757d',
                            cursor: 'pointer',
                            fontSize: '12px'
                          }}
                        >
                          Cancel
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div>
                      <div style={{
                        border: '1px solid #dee2e6',
                        borderRadius: '4px',
                        padding: '10px',
                        backgroundColor: 'white',
                        minHeight: '40px',
                        fontSize: '14px',
                        color: evaluation.annotation ? '#495057' : '#6c757d',
                        fontStyle: evaluation.annotation ? 'normal' : 'italic'
                      }}>
                        {evaluation.annotation || 'No annotation added'}
                      </div>
                      <button
                        onClick={() => startEditingAnnotation(evaluation)}
                        style={{
                          marginTop: '8px',
                          padding: '6px 12px',
                          border: '1px solid #007bff',
                          borderRadius: '4px',
                          backgroundColor: 'white',
                          color: '#007bff',
                          cursor: 'pointer',
                          fontSize: '12px'
                        }}
                      >
                        ✏️ Edit Annotation
                      </button>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      <ContentModal
        isOpen={modalState.isOpen}
        onClose={closeModal}
        title={modalState.title}
        content={modalState.content}
      />
    </div>
  );
};

export default EtrayResultsTable;
